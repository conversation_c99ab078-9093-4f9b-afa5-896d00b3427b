<?php

namespace App\DTOs;

class SimulationDataDTO
{
    public function __construct(
        public array $standings,
        public int $currentWeek,
        public array $predictions,
        public bool $seasonComplete,
        public array $matches,
        public string $seasonId
    ) {}

    public function toArray(): array
    {
        return [
            'standings' => $this->standings,
            'current_week' => $this->currentWeek,
            'predictions' => $this->predictions,
            'season_complete' => $this->seasonComplete,
            'matches' => $this->matches,
            'seasonId' => $this->seasonId
        ];
    }
}
