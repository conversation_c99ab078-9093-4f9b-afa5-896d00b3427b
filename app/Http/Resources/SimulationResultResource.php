<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SimulationResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'fixtures' => $this->resource->fixtures,
            'standings' => $this->resource->standings,
            'predictions' => $this->resource->predictions,
            'current_week' => $this->resource->currentWeek,
            'season_complete' => $this->resource->seasonComplete,
            'seasonId' => $this->resource->seasonId,
        ];
    }
}
