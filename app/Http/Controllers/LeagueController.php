<?php

namespace App\Http\Controllers;

use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use App\Services\SimulationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class LeagueController extends Controller
{
    protected SimulationService $simulationService;

    public function __construct(SimulationService $simulationService)
    {
        $this->simulationService = $simulationService;
    }

    public function getTeams()
    {
        $teams = Team::all();

        return Inertia::render('Teams', [
            'teams' => $teams
        ]);
    }

    public function startSimulation(FixtureGenerator $generator): RedirectResponse
    {
        $teamIds = Team::pluck('id')->toArray();

        $seasonId = Str::random(8);
        $generator->generate($teamIds, $seasonId);

        return redirect()->route('simulation')
            ->cookie('simulation_season_id', $seasonId, 60 * 24 * 7);
    }


    public function simulationIndex(Request $request): Response|RedirectResponse
    {
        $cookieSeasonId = $request->cookie('simulation_season_id');
        $season = LeagueMatches::where('season_id', $cookieSeasonId)->exists();

        if ($season) {
            $seasonId = $cookieSeasonId;
        } else {
            return redirect()->route('/');
        }

        $simulationResult = $this->simulationService->getSimulationResult($seasonId);

        return Inertia::render('Simulation', [
            'fixtures' => $simulationResult->matches,
            'standings' => $simulationResult->standings,
            'predictions' => $simulationResult->predictions,
            'current_week' => $simulationResult->currentWeek,
            'season_complete' => $simulationResult->seasonComplete,
            'seasonId' => $simulationResult->seasonId
        ]);
    }

    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        $updateCount = 0;
        foreach ($matches as $match) {
            $updateCount += $this->simulationService->simulateMatchAsPlayed($match);
        }

        $simulationResult = $this->simulationService->getSimulationResult($seasonId);

        return response()->json([
            'played_count' => $updateCount,
            'fixtures' => $simulationResult->matches,
            'standings' => $simulationResult->standings,
            'predictions' => $simulationResult->predictions,
            'current_week' => $simulationResult->currentWeek,
            'season_complete' => $simulationResult->seasonComplete,
        ]);
    }

    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $unplayedMatches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        foreach ($unplayedMatches as $match) {
            $this->simulationService->simulateMatchAsPlayed($match);
        }

        $simulationResult = $this->simulationService->getSimulationResult($seasonId);

        return response()->json([
            'week' => $nextWeek,
            'fixtures' => $simulationResult->matches,
            'standings' => $simulationResult->standings,
            'predictions' => $simulationResult->predictions,
            'current_week' => $simulationResult->currentWeek,
            'season_complete' => $simulationResult->seasonComplete,
        ]);
    }

}
